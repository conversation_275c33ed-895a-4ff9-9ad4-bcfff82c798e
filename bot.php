<?php
// Powered By Speedx-team.ir --- @AmirSajjad_GH
// Dont Edit This File.
date_default_timezone_set('Asia/Tehran');
require_once 'config.php';

define('API_KEY', $API_KEY);

function loadJsonData($filename)
{
    if (!file_exists($filename)) {
        return [];
    }
    $data = file_get_contents($filename);
    return json_decode($data, true) ?: [];
}

function saveJsonData($filename, $data)
{
    file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function saveUserData($user_data)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    $user_id = $user_data['id'];
    $current_time = date('Y-m-d H:i:s');

    if (isset($users[$user_id])) {
        $users[$user_id] = array_merge($users[$user_id], $user_data);
        $users[$user_id]['last_activity'] = $current_time;
    } else {

        $user_data['join_date'] = $current_time;
        $user_data['last_activity'] = $current_time;
        $users[$user_id] = $user_data;
    }

    saveJsonData($USERS_JSON_FILE, $users);
}

function getUserData($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return isset($users[$user_id]) ? $users[$user_id] : null;
}

function getAllUsers()
{
    global $USERS_JSON_FILE;
    return loadJsonData($USERS_JSON_FILE);
}

function saveChannelData($channel_data)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    $channel_id = $channel_data['id'];
    $current_time = date('Y-m-d H:i:s');

    $channel_data['created_at'] = $current_time;
    $channel_data['updated_at'] = $current_time;
    $channels[$channel_id] = $channel_data;

    saveJsonData($CHANNELS_JSON_FILE, $channels);
}

function isChannelAlreadyAdded($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]);
}

function getChannelOwner($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]) ? $channels[$channel_id]['owner_id'] : null;
}

function getUserChannels($user_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    $user_channels = [];

    foreach ($channels as $channel) {
        if ($channel['owner_id'] == $user_id) {
            $user_channels[] = $channel;
        }
    }

    return $user_channels;
}

function getChannelById($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);
    return isset($channels[$channel_id]) ? $channels[$channel_id] : null;
}

function updateChannelSignature($channel_id, $new_signature)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    if (isset($channels[$channel_id])) {
        $channels[$channel_id]['signature'] = $new_signature;
        $channels[$channel_id]['updated_at'] = date('Y-m-d H:i:s');
        saveJsonData($CHANNELS_JSON_FILE, $channels);
        return true;
    }

    return false;
}

function deleteChannel($channel_id)
{
    global $CHANNELS_JSON_FILE;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    if (isset($channels[$channel_id])) {
        unset($channels[$channel_id]);
        saveJsonData($CHANNELS_JSON_FILE, $channels);
        return true;
    }

    return false;
}

function setUserState($user_id, $state, $data = [])
{
    $user_data = getUserData($user_id);
    if ($user_data) {
        $user_data['state'] = $state;
        $user_data['state_data'] = $data;
        saveUserData($user_data);
    }
}

function getUserState($user_id)
{
    $user_data = getUserData($user_id);
    return $user_data ? ($user_data['state'] ?? null) : null;
}

function getUserStateData($user_id)
{
    $user_data = getUserData($user_id);
    return $user_data ? ($user_data['state_data'] ?? []) : [];
}

function clearUserState($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    if (isset($users[$user_id])) {

        unset($users[$user_id]['state']);
        unset($users[$user_id]['state_data']);
        $users[$user_id]['last_activity'] = date('Y-m-d H:i:s');

        saveJsonData($USERS_JSON_FILE, $users);

        error_log("Cleared state for user $user_id");
    }
}

function forceClearUserState($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);

    if (isset($users[$user_id])) {

        $users[$user_id]['state'] = null;
        $users[$user_id]['state_data'] = null;
        unset($users[$user_id]['state']);
        unset($users[$user_id]['state_data']);
        $users[$user_id]['last_activity'] = date('Y-m-d H:i:s');

        saveJsonData($USERS_JSON_FILE, $users);
        saveJsonData($USERS_JSON_FILE, $users);

        error_log("Force cleared state for user $user_id");
    }
}

function handleSignatureEditing($user_id, $chat_id, $text)
{

    $new_signature = trim($text);
    $state_data = getUserStateData($user_id);

    if (empty($new_signature)) {

        clearUserState($user_id);
        return;
    }

    if (strlen($new_signature) > 200) {

        clearUserState($user_id);
        return;
    }

    if (updateChannelSignature($state_data['channel_id'], $new_signature)) {
        clearUserState($user_id);

        logUserAction($user_id, 'signature_updated', "Signature updated for channel: {$state_data['channel_id']} to: $new_signature");

        $success_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '📣 چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ],
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id,
            "✅ عالی!\n\n" .
            "امضای چنل «{$state_data['channel_title']}» با موفقیت به‌روزرسانی شد.\n\n" .
            "📝 امضای جدید: $new_signature",
            $success_keyboard
        );
    } else {
        sendmessage($chat_id, "❌ خطا در به‌روزرسانی امضا! لطفا دوباره تلاش کنید.");
    }
}

function checkBotAdminStatus($channel_username)
{
    $bot_info = bot('getMe');
    if (!$bot_info || !isset($bot_info->result)) {
        return false;
    }

    $bot_id = $bot_info->result->id;

    $result = bot('getChatMember', [
        'chat_id' => $channel_username,
        'user_id' => $bot_id
    ]);

    if (!$result || !isset($result->result)) {
        return false;
    }

    return in_array($result->result->status, ['administrator', 'creator']);
}

function getChannelInfo($channel_identifier)
{
    $result = bot('getChat', [
        'chat_id' => $channel_identifier
    ]);

    if (!$result || !isset($result->result)) {
        return false;
    }

    return $result->result;
}

function isValidChannelIdentifier($identifier)
{
    $identifier = trim($identifier);

    if (is_numeric($identifier) || (substr($identifier, 0, 1) === '-' && is_numeric(substr($identifier, 1)))) {
        return true;
    }

    $username_without_at = ltrim($identifier, '@');
    if (strlen($username_without_at) >= 5 && preg_match('/^[a-zA-Z0-9_]+$/', $username_without_at)) {
        return true;
    }

    return false;
}

function getUsersCount()
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return count($users);
}

function isUserExists($user_id)
{
    global $USERS_JSON_FILE;
    $users = loadJsonData($USERS_JSON_FILE);
    return isset($users[$user_id]);
}

function logUserAction($user_id, $action, $details = '')
{
    $user_data = getUserData($user_id);
    if ($user_data) {
        if (!isset($user_data['actions_log'])) {
            $user_data['actions_log'] = [];
        }

        $user_data['actions_log'][] = [
            'action' => $action,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if (count($user_data['actions_log']) > 50) {
            $user_data['actions_log'] = array_slice($user_data['actions_log'], -50);
        }

        saveUserData($user_data);
    }
}

function isAdmin($user_id)
{
    global $admin_users;
    return in_array($user_id, $admin_users);
}

function getBotStatus()
{
    global $BOT_STATUS_FILE;
    if (!file_exists($BOT_STATUS_FILE)) {
        // Default status is enabled
        setBotStatus(true);
        return true;
    }

    $status_data = loadJsonData($BOT_STATUS_FILE);
    return isset($status_data['enabled']) ? $status_data['enabled'] : true;
}

function setBotStatus($enabled)
{
    global $BOT_STATUS_FILE;
    $status_data = [
        'enabled' => $enabled,
        'updated_at' => date('Y-m-d H:i:s'),
        'updated_by' => 'system'
    ];
    saveJsonData($BOT_STATUS_FILE, $status_data);
}

function setBotStatusByAdmin($enabled, $admin_id)
{
    global $BOT_STATUS_FILE;
    $status_data = [
        'enabled' => $enabled,
        'updated_at' => date('Y-m-d H:i:s'),
        'updated_by' => $admin_id
    ];
    saveJsonData($BOT_STATUS_FILE, $status_data);
}

function isBotEnabled()
{
    return getBotStatus();
}

function broadcastMessage($message_text, $admin_id)
{
    $users = getAllUsers();
    $total_users = count($users);
    $sent_count = 0;
    $failed_count = 0;

    foreach ($users as $user_id => $user_data) {
        $chat_id = $user_data['chat_id'] ?? $user_id;

        try {
            $result = bot('sendMessage', [
                'chat_id' => $chat_id,
                'text' => $message_text
            ]);

            if ($result && isset($result->ok) && $result->ok) {
                $sent_count++;
            } else {
                $failed_count++;
            }

            // Small delay to avoid hitting rate limits
            usleep(50000); // 50ms delay

        } catch (Exception $e) {
            $failed_count++;
            error_log("Broadcast failed for user $user_id: " . $e->getMessage());
        }
    }

    // Log broadcast activity
    logUserAction($admin_id, 'broadcast_sent', "Broadcast sent to $sent_count users, $failed_count failed");

    return [
        'total' => $total_users,
        'sent' => $sent_count,
        'failed' => $failed_count
    ];
}

function createBroadcastKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '❌ انصراف',
                'callback_data' => 'admin_panel'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function getBroadcastMessage()
{
    $users_count = getUsersCount();

    return "📢 ارسال پیام همگانی\n\n" .
           "👥 تعداد کاربران: $users_count\n\n" .
           "لطفا متن پیامی که می‌خواهید به تمام کاربران ارسال شود را تایپ کنید:\n\n" .
           "💡 نکات مهم:\n" .
           "• پیام به صورت متن ساده ارسال خواهد شد\n" .
           "• پیام به تمام کاربران ثبت شده ارسال خواهد شد\n" .
           "• این عمل قابل بازگشت نیست\n\n" .
           "⚠️ لطفا با دقت پیام خود را تایپ کنید.";
}

function createAdminPanelKeyboard()
{
    $bot_enabled = isBotEnabled();
    $status_text = $bot_enabled ? '🔴 خاموش کردن ربات' : '🟢 روشن کردن ربات';
    $status_callback = $bot_enabled ? 'admin_disable_bot' : 'admin_enable_bot';

    $keyboard = [
        [
            [
                'text' => '👥 کاربران',
                'callback_data' => 'admin_users'
            ],
            [
                'text' => '📺 چنل ها',
                'callback_data' => 'admin_channels'
            ]
        ],
        [
            [
                'text' => '📢 ارسال همگانی',
                'callback_data' => 'admin_broadcast'
            ],
            [
                'text' => $status_text,
                'callback_data' => $status_callback
            ]
        ],
        [
            [
                'text' => '🔙 بازگشت به منوی اصلی',
                'callback_data' => 'back_to_main'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function getAdminPanelMessage()
{
    $users_count = getUsersCount();
    $channels = loadJsonData($GLOBALS['CHANNELS_JSON_FILE']);
    $channels_count = count($channels);
    $bot_enabled = isBotEnabled();
    $status_text = $bot_enabled ? '🟢 فعال' : '🔴 غیرفعال';

    return "🔧 پنل مدیریت ربات\n\n" .
           "📊 آمار کلی:\n" .
           "👥 تعداد کاربران: $users_count\n" .
           "📺 تعداد چنل ها: $channels_count\n" .
           "🤖 وضعیت ربات: $status_text\n\n" .
           "لطفا بخش مورد نظر خود را انتخاب کنید:";
}

function getUsersListMessage($page = 1, $per_page = 10)
{
    $users = getAllUsers();
    $total_users = count($users);
    $total_pages = ceil($total_users / $per_page);
    $offset = ($page - 1) * $per_page;

    $users_slice = array_slice($users, $offset, $per_page, true);

    $message = "👥 لیست کاربران (صفحه $page از $total_pages)\n\n";
    $message .= "📊 تعداد کل کاربران: $total_users\n\n";

    foreach ($users_slice as $user_id => $user) {
        $name = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
        $username = $user['username'] ? '@' . $user['username'] : 'بدون یوزرنیم';
        $join_date = $user['join_date'] ?? 'نامشخص';
        $last_activity = $user['last_activity'] ?? 'نامشخص';

        $message .= "👤 $name\n";
        $message .= "🆔 آیدی: $user_id\n";
        $message .= "📱 یوزرنیم: $username\n";
        $message .= "📅 تاریخ عضویت: $join_date\n";
        $message .= "⏰ آخرین فعالیت: $last_activity\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
    }

    return $message;
}

function getChannelsListMessage($page = 1, $per_page = 10)
{
    $channels = loadJsonData($GLOBALS['CHANNELS_JSON_FILE']);
    $total_channels = count($channels);
    $total_pages = ceil($total_channels / $per_page);
    $offset = ($page - 1) * $per_page;

    $channels_slice = array_slice($channels, $offset, $per_page, true);

    $message = "📺 لیست چنل ها (صفحه $page از $total_pages)\n\n";
    $message .= "📊 تعداد کل چنل ها: $total_channels\n\n";

    foreach ($channels_slice as $channel_id => $channel) {
        $title = $channel['title'] ?? 'نامشخص';
        $identifier = $channel['identifier'] ?? $channel_id;
        $owner_id = $channel['owner_id'] ?? 'نامشخص';
        $signature = $channel['signature'] ?? 'بدون امضا';
        $created_at = $channel['created_at'] ?? 'نامشخص';

        $message .= "📺 $title\n";
        $message .= "🆔 شناسه: $identifier\n";
        $message .= "👤 مالک: $owner_id\n";
        $message .= "📝 امضا: " . (strlen($signature) > 50 ? substr($signature, 0, 50) . '...' : $signature) . "\n";
        $message .= "📅 تاریخ ایجاد: $created_at\n";
        $message .= "━━━━━━━━━━━━━━━━━━━━\n";
    }

    return $message;
}

function createUsersListKeyboard($page = 1, $per_page = 10)
{
    $users = getAllUsers();
    $total_users = count($users);
    $total_pages = ceil($total_users / $per_page);

    $keyboard = [];

    // Navigation buttons
    $nav_buttons = [];
    if ($page > 1) {
        $nav_buttons[] = [
            'text' => '⬅️ قبلی',
            'callback_data' => 'admin_users_page_' . ($page - 1)
        ];
    }
    if ($page < $total_pages) {
        $nav_buttons[] = [
            'text' => 'بعدی ➡️',
            'callback_data' => 'admin_users_page_' . ($page + 1)
        ];
    }

    if (!empty($nav_buttons)) {
        $keyboard[] = $nav_buttons;
    }

    // Back button
    $keyboard[] = [
        [
            'text' => '🔙 بازگشت به پنل ادمین',
            'callback_data' => 'admin_panel'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createChannelsListKeyboard($page = 1, $per_page = 10)
{
    $channels = loadJsonData($GLOBALS['CHANNELS_JSON_FILE']);
    $total_channels = count($channels);
    $total_pages = ceil($total_channels / $per_page);

    $keyboard = [];

    // Navigation buttons
    $nav_buttons = [];
    if ($page > 1) {
        $nav_buttons[] = [
            'text' => '⬅️ قبلی',
            'callback_data' => 'admin_channels_page_' . ($page - 1)
        ];
    }
    if ($page < $total_pages) {
        $nav_buttons[] = [
            'text' => 'بعدی ➡️',
            'callback_data' => 'admin_channels_page_' . ($page + 1)
        ];
    }

    if (!empty($nav_buttons)) {
        $keyboard[] = $nav_buttons;
    }

    // Back button
    $keyboard[] = [
        [
            'text' => '🔙 بازگشت به پنل ادمین',
            'callback_data' => 'admin_panel'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    $channel_buttons = [];
    $channel_names = ['گروه', 'چنل'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }

    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createMainMenuKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '📣 چنل های من',
                'callback_data' => 'my_channels'
            ],
            [
                'text' => '➕ افزودن چنل',
                'callback_data' => 'add_channel'
            ]
        ],
        [
            [
                'text' => '📚 راهنما',
                'callback_data' => 'help'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function eqi($str1, $str2)
{
    return strtolower(trim($str1)) === strtolower(trim($str2));
}

function getMainMenuMessage($first_name)
{
    return "سلام $first_name 👋\n\n" .
           "به ربات لینک چسبون خوش آمدید!\n\n" .
           "این ربات به شما کمک می‌کند تا پست های کانال خود را به صورت کاملا خودکار ویرایش کنید.\n\n" .
           "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";
}

function getHelpMessage()
{
    return "📚 راهنمای کامل ربات لینک چسبون\n\n" .
           "🔹 <b>این ربات چه کاری انجام می‌دهد؟</b>\n" .
           "این ربات به صورت خودکار امضای اختصاصی شما را به انتهای تمام پست‌های کانال اضافه می‌کند.\n\n" .

           "🔹 <b>مراحل استفاده:</b>\n" .
           "1️⃣ ربات را در کانال خود ادمین کنید\n" .
           "2️⃣ از منوی اصلی روی «➕ افزودن چنل» کلیک کنید\n" .
           "3️⃣ یوزرنیم یا آیدی کانال را ارسال کنید\n" .
           "4️⃣ امضای دلخواه خود را تایپ کنید\n" .
           "5️⃣ تمام! از این به بعد تمام پست‌ها امضا خواهند داشت\n\n" .

           "🔹 <b>انواع کانال‌های پشتیبانی شده:</b>\n" .
           "📢 <b>کانال عمومی:</b> @channel_username\n" .
           "🔒 <b>کانال خصوصی:</b> -1001234567890\n\n" .

           "🔹 <b>نحوه دریافت آیدی کانال خصوصی:</b>\n" .
           "• از ربات @userinfobot استفاده کنید\n" .
           "• یا از @RawDataBot کمک بگیرید\n" .
           "• آیدی کانال‌های خصوصی با -100 شروع می‌شوند\n\n" .

           "🔹 <b>مدیریت کانال‌ها:</b>\n" .
           "• از منوی «📣 چنل های من» می‌توانید:\n" .
           "  - امضای کانال را تغییر دهید\n" .
           "  - کانال را حذف کنید\n" .
           "  - اطلاعات کانال را مشاهده کنید\n\n" .

           "🔹 <b>نکات مهم:</b>\n" .
           "⚠️ ربات باید در کانال ادمین باشد\n" .
           "⚠️ هر کانال فقط یک بار قابل اضافه کردن است\n" .
           "⚠️ امضا حداکثر 200 کاراکتر می‌تواند باشد\n" .
           "⚠️ امضا به پست‌های فوروارد شده اضافه نمی‌شود\n\n" .

           "🔹 <b>مثال امضا:</b>\n" .
           "<code>@YourChannel | کانال رسمی ما\n🔗 t.me/YourChannel</code>\n\n" .

           "❓ در صورت بروز مشکل، مجدداً ربات را ادمین کنید و دوباره تلاش کنید.";
}

$update = json_decode(file_get_contents('php://input'));

if (isset($update->channel_post)) {
    $post = $update->channel_post;
    $chat_id = $post->chat->id;
    $chat_username = $post->chat->username ?? null;

    $channel = null;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    foreach ($channels as $ch) {
        if ($ch['id'] == $chat_id ||
            ($chat_username && isset($ch['username']) && $ch['username'] == '@' . $chat_username) ||
            (isset($ch['identifier']) && $ch['identifier'] == $chat_id)) {
            $channel = $ch;
            break;
        }
    }

    if ($channel && isset($channel['signature']) && !empty($channel['signature'])) {

        if (!isset($post->forward_from) && !isset($post->forward_from_chat)) {
            $text = $post->text ?? $post->caption ?? '';

            $text = preg_replace('/(\n|^).*@.*(\n|$)/', "\n", $text);
            $text = trim($text);

            $text .= "\n\n" . $channel['signature'];

            if (isset($post->text)) {

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'text' => $text
                ]);
            } elseif (isset($post->caption)) {

                bot('editMessageCaption', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'caption' => $text
                ]);
            }
        }
    }
    exit;
}

if (isset($update->edited_channel_post)) {
    $post = $update->edited_channel_post;
    $chat_id = $post->chat->id;
    $chat_username = $post->chat->username ?? null;

    $channel = null;
    $channels = loadJsonData($CHANNELS_JSON_FILE);

    foreach ($channels as $ch) {
        if ($ch['id'] == $chat_id ||
            ($chat_username && isset($ch['username']) && $ch['username'] == '@' . $chat_username) ||
            (isset($ch['identifier']) && $ch['identifier'] == $chat_id)) {
            $channel = $ch;
            break;
        }
    }

    if ($channel && isset($channel['signature']) && !empty($channel['signature'])) {

        if (!isset($post->forward_from) && !isset($post->forward_from_chat)) {
            $text = $post->text ?? $post->caption ?? '';

            $text = preg_replace('/(\n|^).*@.*(\n|$)/', "\n", $text);
            $text = trim($text);

            $text .= "\n\n" . $channel['signature'];

            if (isset($post->text)) {

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'text' => $text
                ]);
            } elseif (isset($post->caption)) {

                bot('editMessageCaption', [
                    'chat_id' => $chat_id,
                    'message_id' => $post->message_id,
                    'caption' => $text
                ]);
            }
        }
    }
    exit;
}

if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    // Check if bot is enabled (except for admin panel actions)
    if (!isBotEnabled() && !isAdmin($user_id) && strpos($data, 'admin_') !== 0) {
        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => '🔴 ربات در حال حاضر غیرفعال است!',
            'show_alert' => true
        ]);
        return;
    }

    $user_data = [
        'id' => $user_id,
        'first_name' => $callback_query->from->first_name ?? '',
        'last_name' => $callback_query->from->last_name ?? '',
        'username' => $callback_query->from->username ?? '',
        'language_code' => $callback_query->from->language_code ?? '',
        'chat_id' => $chat_id
    ];
    saveUserData($user_data);

    if ($data == 'check_membership') {
        logUserAction($user_id, 'check_membership', 'User clicked check membership button');
        $join_keyboard = createJoinKeyboard($required_channels);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'membership_verified', 'User successfully verified membership');
            $first_name = $callback_query->from->first_name ?? 'کاربر';
            $main_menu_keyboard = createMainMenuKeyboard();
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ]);
        } else {
            logUserAction($user_id, 'membership_failed', 'User failed membership verification');
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ شما هنوز عضو نیستید!\n\n" .
                         "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
        }
    } elseif ($data == 'help') {
        logUserAction($user_id, 'help_clicked', 'User clicked help button');

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'help_access_denied', 'User tried to access help without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای مشاهده راهنما، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $help_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getHelpMessage(),
            'reply_markup' => $help_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif ($data == 'add_channel') {
        logUserAction($user_id, 'add_channel_clicked', 'User clicked add channel button');

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'add_channel_access_denied', 'User tried to add channel without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای افزودن چنل، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        setUserState($user_id, 'adding_channel_step1');

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '� افزودن مستقیم چنل',
                        'url' => 'https://t.me/LinkChasboon_Bot?startchannel=start'
                    ]
                ],
                [
                    [
                        'text' => '�🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "📣 افزودن چنل جدید\n\n" .
                     "برای افزودن چنل جدید، لطفا مراحل زیر را دنبال کنید:\n\n" .
                     "1️⃣ ابتدا ربات را در چنل خود ادمین کنید\n" .
                     "2️⃣ سپس یوزرنیم یا آیدی چنل را ارسال کنید\n\n" .
                     "💡 نوع چنل‌های پشتیبانی شده:\n" .
                     "🔸 چنل عمومی: @channel_username\n" .
                     "🔸 چنل خصوصی: -1001234567890\n\n" .
                     "⚠️ توجه: ربات باید دسترسی ادمین داشته باشد تا بتواند پست‌ها را ویرایش کند.\n\n" .
                     "لطفا یوزرنیم یا آیدی چنل خود را ارسال کنید:",
            'reply_markup' => $back_keyboard
        ]);
    } elseif ($data == 'my_channels') {
        logUserAction($user_id, 'my_channels_clicked', 'User clicked my channels button');

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'my_channels_access_denied', 'User tried to access channels without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای مشاهده چنل‌های خود، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $user_channels = getUserChannels($user_id);

        if (empty($user_channels)) {
            $keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '➕ افزودن چنل',
                            'callback_data' => 'add_channel'
                        ],
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "📣 چنل های من\n\n" .
                         "شما هنوز هیچ چنلی ثبت نکرده‌اید.\n\n" .
                         "برای شروع، روی دکمه «افزودن چنل» کلیک کنید.",
                'reply_markup' => $keyboard
            ]);
        } else {
            $channels_text = "📣 چنل های من\n\n";
            $channels_text .= "لطفا چنل مورد نظر خود را انتخاب کنید:\n\n";
            $keyboard_buttons = [];

            foreach ($user_channels as $index => $channel) {
                $keyboard_buttons[] = [
                    [
                        'text' => ($channel['title'] ?? 'نامشخص'),
                        'callback_data' => 'manage_channel_' . $channel['id']
                    ]
                ];
            }

            $keyboard_buttons[] = [
                [
                    'text' => '➕ افزودن چنل جدید',
                    'callback_data' => 'add_channel'
                ],
                [
                    'text' => '🔙 بازگشت به منوی اصلی',
                    'callback_data' => 'back_to_main'
                ]
            ];

            $keyboard = json_encode(['inline_keyboard' => $keyboard_buttons]);

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $channels_text,
                'reply_markup' => $keyboard
            ]);
        }
    } elseif ($data == 'back_to_main') {
        clearUserState($user_id);
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif (strpos($data, 'manage_channel_') === 0) {
        $channel_id = str_replace('manage_channel_', '', $data);

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'channel_management_access_denied', 'User tried to manage channel without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای مدیریت چنل، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'channel_management_opened', "Opened management for channel: {$channel['identifier']}");

        $management_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '✏️ تغییر امضا',
                        'callback_data' => 'edit_signature_' . $channel_id
                    ],
                    [
                        'text' => '🗑 حذف چنل',
                        'callback_data' => 'delete_channel_' . $channel_id
                    ]
                ],
                [
                    [
                        'text' => '🔙 بازگشت به چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        $channel_info = "📺 مدیریت چنل\n\n";
        $channel_info .= "📣 نام چنل: " . ($channel['title'] ?? 'نامشخص') . "\n";

        if (isset($channel['username']) && !empty($channel['username'])) {

            $username_display = $channel['username'];
            if (substr($username_display, 0, 1) !== '@') {
                $username_display = '@' . $username_display;
            }
            $channel_info .= "🆔 یوزرنیم: " . $username_display . "\n";
        } else {
            $channel_info .= "🆔 آیدی چنل: " . $channel['identifier'] . "\n";
        }

        $channel_info .= "📝 امضای فعلی: " . $channel['signature'] . "\n";
        $channel_info .= "📅 تاریخ ایجاد: " . $channel['created_at'] . "\n\n";
        $channel_info .= "لطفا عملیات مورد نظر خود را انتخاب کنید:";

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $channel_info,
            'reply_markup' => $management_keyboard
        ]);
    } elseif (strpos($data, 'edit_signature_') === 0) {
        $channel_id = str_replace('edit_signature_', '', $data);

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'edit_signature_access_denied', 'User tried to edit signature without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای ویرایش امضا، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'edit_signature_started', "Started editing signature for channel: {$channel['identifier']}");

        setUserState($user_id, 'editing_signature', [
            'channel_id' => $channel_id,
            'channel_title' => $channel['title'],
            'current_signature' => $channel['signature']
        ]);

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به چنل های من',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "✏️ ویرایش امضا\n\n" .
                     "چنل: {$channel['title']}\n" .
                     "امضای فعلی: {$channel['signature']}\n\n" .
                     "لطفا امضای جدید را ارسال کنید:",
            'reply_markup' => $back_keyboard
        ]);
    } elseif (strpos($data, 'delete_channel_') === 0) {
        $channel_id = str_replace('delete_channel_', '', $data);

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'delete_channel_access_denied', 'User tried to delete channel without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای حذف چنل، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        $confirm_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '✅ بله، حذف کن',
                        'callback_data' => 'confirm_delete_' . $channel_id
                    ],
                    [
                        'text' => '❌ انصراف',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ]
        ]);

        $delete_text = "🗑 حذف چنل\n\n" .
                      "آیا مطمئن هستید که می‌خواهید چنل زیر را حذف کنید؟\n\n" .
                      "📣 {$channel['title']}\n";

        if (isset($channel['username']) && !empty($channel['username'])) {
            $username_display = $channel['username'];
            if (substr($username_display, 0, 1) !== '@') {
                $username_display = '@' . $username_display;
            }
            $delete_text .= "🆔 یوزرنیم: " . $username_display . "\n\n";
        } else {
            $delete_text .= "🆔 آیدی چنل: " . $channel['identifier'] . "\n\n";
        }

        $delete_text .= "⚠️ این عمل قابل بازگشت نیست!";

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $delete_text,
            'reply_markup' => $confirm_keyboard
        ]);
    } elseif (strpos($data, 'confirm_delete_') === 0) {
        $channel_id = str_replace('confirm_delete_', '', $data);

        if (!checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'confirm_delete_access_denied', 'User tried to confirm delete without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "⚠️ دسترسی محدود\n\n" .
                         "برای تأیید حذف چنل، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
            return;
        }

        $channel = getChannelById($channel_id);

        if (!$channel || $channel['owner_id'] != $user_id) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا! چنل یافت نشد یا شما مالک آن نیستید.',
                'show_alert' => true
            ]);
            return;
        }

        if (deleteChannel($channel_id)) {
            logUserAction($user_id, 'channel_deleted', "Channel deleted: {$channel['identifier']}");

            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '✅ چنل با موفقیت حذف شد.',
                'show_alert' => true
            ]);

            $user_channels = getUserChannels($user_id);

            if (empty($user_channels)) {
                $keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '➕ افزودن چنل',
                                'callback_data' => 'add_channel'
                            ],
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => "📣 چنل های من\n\n" .
                             "شما هنوز هیچ چنلی ثبت نکرده‌اید.\n\n" .
                             "برای شروع، روی دکمه «افزودن چنل» کلیک کنید.",
                    'reply_markup' => $keyboard
                ]);
            } else {

                $channels_text = "📣 چنل های من\n\n";
                $channels_text .= "لطفا چنل مورد نظر خود را انتخاب کنید:\n\n";
                $keyboard_buttons = [];

                foreach ($user_channels as $index => $ch) {
                    $keyboard_buttons[] = [
                        [
                            'text' => ($ch['title'] ?? 'نامشخص'),
                            'callback_data' => 'manage_channel_' . $ch['id']
                        ]
                    ];
                }

                $keyboard_buttons[] = [
                    [
                        'text' => '➕ افزودن چنل جدید',
                        'callback_data' => 'add_channel'
                    ],
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ];

                $keyboard = json_encode(['inline_keyboard' => $keyboard_buttons]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $channels_text,
                    'reply_markup' => $keyboard
                ]);
            }
        } else {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ خطا در حذف چنل!',
                'show_alert' => true
            ]);
        }
    } elseif ($data == 'admin_panel') {
        // Admin panel access
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'admin_panel_accessed', 'Admin accessed admin panel');

        $admin_keyboard = createAdminPanelKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getAdminPanelMessage(),
            'reply_markup' => $admin_keyboard
        ]);
    } elseif ($data == 'admin_users' || strpos($data, 'admin_users_page_') === 0) {
        // Admin users list
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        $page = 1;
        if (strpos($data, 'admin_users_page_') === 0) {
            $page = (int)str_replace('admin_users_page_', '', $data);
        }

        logUserAction($user_id, 'admin_users_viewed', "Admin viewed users list page $page");

        $users_message = getUsersListMessage($page);
        $users_keyboard = createUsersListKeyboard($page);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $users_message,
            'reply_markup' => $users_keyboard
        ]);
    } elseif ($data == 'admin_channels' || strpos($data, 'admin_channels_page_') === 0) {
        // Admin channels list
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        $page = 1;
        if (strpos($data, 'admin_channels_page_') === 0) {
            $page = (int)str_replace('admin_channels_page_', '', $data);
        }

        logUserAction($user_id, 'admin_channels_viewed', "Admin viewed channels list page $page");

        $channels_message = getChannelsListMessage($page);
        $channels_keyboard = createChannelsListKeyboard($page);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $channels_message,
            'reply_markup' => $channels_keyboard
        ]);
    } elseif ($data == 'admin_enable_bot') {
        // Enable bot
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        setBotStatusByAdmin(true, $user_id);
        logUserAction($user_id, 'bot_enabled', 'Admin enabled the bot');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => '✅ ربات با موفقیت روشن شد!',
            'show_alert' => true
        ]);

        // Update admin panel
        $admin_keyboard = createAdminPanelKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getAdminPanelMessage(),
            'reply_markup' => $admin_keyboard
        ]);
    } elseif ($data == 'admin_disable_bot') {
        // Disable bot
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        setBotStatusByAdmin(false, $user_id);
        logUserAction($user_id, 'bot_disabled', 'Admin disabled the bot');

        bot('answerCallbackQuery', [
            'callback_query_id' => $callback_query->id,
            'text' => '🔴 ربات با موفقیت خاموش شد!',
            'show_alert' => true
        ]);

        // Update admin panel
        $admin_keyboard = createAdminPanelKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getAdminPanelMessage(),
            'reply_markup' => $admin_keyboard
        ]);
    } elseif ($data == 'admin_broadcast') {
        // Admin broadcast message
        if (!isAdmin($user_id)) {
            bot('answerCallbackQuery', [
                'callback_query_id' => $callback_query->id,
                'text' => '❌ شما دسترسی ادمین ندارید!',
                'show_alert' => true
            ]);
            return;
        }

        logUserAction($user_id, 'broadcast_started', 'Admin started broadcast message');

        setUserState($user_id, 'admin_broadcasting');

        $broadcast_keyboard = createBroadcastKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getBroadcastMessage(),
            'reply_markup' => $broadcast_keyboard
        ]);
    }
    exit;
}

if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    // Check if bot is enabled (except for admin commands)
    if (!isBotEnabled() && !isAdmin($user_id) && $text != "/apanel") {
        sendmessage($chat_id, "🔴 ربات در حال حاضر غیرفعال است!\n\nلطفا منتظر بمانید تا ربات مجدداً فعال شود.");
        return;
    }

    $user_data = [
        'id' => $user_id,
        'first_name' => $message->from->first_name ?? '',
        'last_name' => $message->from->last_name ?? '',
        'username' => $message->from->username ?? '',
        'language_code' => $message->from->language_code ?? '',
        'chat_id' => $chat_id
    ];
    saveUserData($user_data);

    if ($text == "/start") {
        clearUserState($user_id); 
        $first_name = $message->from->first_name ?? 'کاربر';

        logUserAction($user_id, 'command_used', "User used command: $text");

        if (checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'bot_access_granted', 'User accessed bot with valid membership');
            $main_menu_keyboard = createMainMenuKeyboard();
            $data = [
                'chat_id' => $chat_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ];
            bot('sendMessage', $data);
        } else {
            logUserAction($user_id, 'bot_access_denied', 'User tried to access bot without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                $join_keyboard
            );
        }
        return; 
    } elseif ($text == "/help") {
        clearUserState($user_id); 
        $first_name = $message->from->first_name ?? 'کاربر';

        logUserAction($user_id, 'command_used', "User used command: $text");

        if (checkUserMembership($user_id, $required_channels)) {
            logUserAction($user_id, 'help_command_used', 'User used /help command with valid membership');

            $help_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            $data = [
                'chat_id' => $chat_id,
                'text' => getHelpMessage(),
                'reply_markup' => $help_keyboard,
                'parse_mode' => 'HTML'
            ];
            bot('sendMessage', $data);
        } else {
            logUserAction($user_id, 'help_access_denied', 'User tried to access help without valid membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات و مشاهده راهنما، لطفا ابتدا در کانال‌های زیر عضو شوید:",
                $join_keyboard
            );
        }
        return;
    } elseif ($text == "/apanel") {
        clearUserState($user_id);

        logUserAction($user_id, 'command_used', "User used command: $text");

        // Check if user is admin
        if (!isAdmin($user_id)) {
            sendmessage($chat_id, "❌ شما دسترسی ادمین ندارید!");
            return;
        }

        logUserAction($user_id, 'admin_panel_command_used', 'Admin used /apanel command');

        $admin_keyboard = createAdminPanelKeyboard();
        sendmessage($chat_id, getAdminPanelMessage(), $admin_keyboard);
        return;
    }

    $user_state = getUserState($user_id);

    if ($user_state) {
        error_log("User $user_id has state: $user_state");
    }

    if ($user_state == 'adding_channel_step1') {

        $channel_identifier = trim($text);

        if (!isValidChannelIdentifier($channel_identifier)) {

            clearUserState($user_id);

            return;
        }

        if (is_numeric($channel_identifier) || (substr($channel_identifier, 0, 1) === '-' && is_numeric(substr($channel_identifier, 1)))) {

            $formatted_identifier = $channel_identifier;
        } else {

            if (substr($channel_identifier, 0, 1) !== '@') {
                $formatted_identifier = '@' . $channel_identifier;
            } else {
                $formatted_identifier = $channel_identifier;
            }
        }

        logUserAction($user_id, 'channel_identifier_provided', "Channel identifier: $formatted_identifier");

        if (!checkBotAdminStatus($formatted_identifier)) {
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ خطا!\n\n" .
                "ربات در چنل مورد نظر ادمین نیست یا چنل وجود ندارد.\n\n" .
                "لطفا:\n" .
                "1️⃣ ابتدا ربات را در چنل خود ادمین کنید\n" .
                "2️⃣ مطمئن شوید یوزرنیم یا آیدی چنل صحیح است\n" .
                "3️⃣ دوباره یوزرنیم یا آیدی چنل را ارسال کنید",
                $back_keyboard
            );
            return;
        }

        $channel_info = getChannelInfo($formatted_identifier);
        if (!$channel_info) {
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ خطا!\n\n" .
                "نمی‌توانم اطلاعات چنل مورد نظر را دریافت کنم.\n\n" .
                "لطفا یوزرنیم یا آیدی صحیح چنل را ارسال کنید.",
                $back_keyboard
            );
            return;
        }

        if (isChannelAlreadyAdded($channel_info->id)) {
            $existing_owner = getChannelOwner($channel_info->id);
            $back_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            if ($existing_owner == $user_id) {

                sendmessage($chat_id,
                    "❌ خطا!\n\n" .
                    "شما قبلاً این چنل را اضافه کرده‌اید.\n\n" .
                    "هر چنل فقط یک بار قابل اضافه کردن است.\n\n" .
                    "برای مدیریت چنل از منوی «چنل های من» استفاده کنید.",
                    $back_keyboard
                );
            } else {

                sendmessage($chat_id,
                    "❌ خطا!\n\n" .
                    "این چنل قبلاً توسط کاربر دیگری اضافه شده است.\n\n" .
                    "هر چنل فقط می‌تواند توسط یک کاربر مدیریت شود.\n\n" .
                    "لطفا چنل دیگری را امتحان کنید.",
                    $back_keyboard
                );
            }
            return;
        }

        setUserState($user_id, 'adding_channel_step2', [
            'identifier' => $formatted_identifier,
            'username' => $channel_info->username ?? null,
            'title' => $channel_info->title,
            'id' => $channel_info->id,
            'type' => $channel_info->type ?? 'channel'
        ]);

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        $channel_type_text = is_numeric($formatted_identifier) || (substr($formatted_identifier, 0, 1) === '-') ? 'خصوصی' : 'عمومی';

        sendmessage($chat_id,
            "✅ عالی!\n\n" .
            "چنل «{$channel_info->title}» ({$channel_type_text}) با موفقیت تأیید شد.\n\n" .
            "حالا لطفا امضای اختصاصی برای این چنل ارسال کنید:\n\n" .
            "💡 مثال: @YourChannel | کانال رسمی ما",
            $back_keyboard
        );

    } elseif ($user_state == 'adding_channel_step2') {

        $signature = trim($text);
        $state_data = getUserStateData($user_id);

        if (empty($signature)) {

            clearUserState($user_id);
            return;
        }

        if (strlen($signature) > 200) {

            clearUserState($user_id);
            return;
        }

        $channel_data = [
            'id' => $state_data['id'],
            'identifier' => $state_data['identifier'],
            'username' => $state_data['username'],
            'title' => $state_data['title'],
            'type' => $state_data['type'],
            'signature' => $signature,
            'owner_id' => $user_id,
            'status' => 'active'
        ];

        saveChannelData($channel_data);

        error_log("About to clear state for user $user_id after adding channel");
        clearUserState($user_id);

        $check_state = getUserState($user_id);
        error_log("State after first clear: " . ($check_state ? $check_state : 'null'));

        logUserAction($user_id, 'channel_added', "Channel added: {$state_data['identifier']} with signature: $signature");

        $main_menu_keyboard = createMainMenuKeyboard();
        sendmessage($chat_id,
            "🎉 تبریک!\n\n" .
            "چنل «{$state_data['title']}» با موفقیت اضافه شد.\n\n" .
            "📝 امضای ثبت شده: $signature\n\n" .
            "حالا می‌توانید از منوی «چنل های من» چنل خود را مشاهده کنید.",
            $main_menu_keyboard
        );

        error_log("Clearing state again for user $user_id after sending success message");
        clearUserState($user_id);

        $final_state = getUserState($user_id);
        error_log("Final state after second clear: " . ($final_state ? $final_state : 'null'));

        if ($final_state !== null) {
            error_log("State still exists, using force clear for user $user_id");
            forceClearUserState($user_id);
            $force_check = getUserState($user_id);
            error_log("State after force clear: " . ($force_check ? $force_check : 'null'));
        }

    } elseif ($user_state == 'editing_signature') {
        handleSignatureEditing($user_id, $chat_id, $text);
    } elseif ($user_state == 'admin_broadcasting') {
        // Handle admin broadcast message
        if (!isAdmin($user_id)) {
            clearUserState($user_id);
            sendmessage($chat_id, "❌ شما دسترسی ادمین ندارید!");
            return;
        }

        $broadcast_text = trim($text);

        if (empty($broadcast_text)) {
            sendmessage($chat_id, "❌ پیام نمی‌تواند خالی باشد! لطفا متن پیام را ارسال کنید.");
            return;
        }

        if (strlen($broadcast_text) > 4000) {
            sendmessage($chat_id, "❌ پیام خیلی طولانی است! حداکثر 4000 کاراکتر مجاز است.");
            return;
        }

        clearUserState($user_id);

        // Show confirmation message
        sendmessage($chat_id, "⏳ در حال ارسال پیام به تمام کاربران...\n\nلطفا صبر کنید...");

        // Send broadcast
        $result = broadcastMessage($broadcast_text, $user_id);

        // Show result
        $result_message = "📢 گزارش ارسال همگانی\n\n" .
                         "👥 تعداد کل کاربران: {$result['total']}\n" .
                         "✅ ارسال موفق: {$result['sent']}\n" .
                         "❌ ارسال ناموفق: {$result['failed']}\n\n" .
                         "📝 متن ارسال شده:\n" .
                         "━━━━━━━━━━━━━━━━━━━━\n" .
                         $broadcast_text;

        $admin_keyboard = createAdminPanelKeyboard();
        sendmessage($chat_id, $result_message, $admin_keyboard);

    } else {

        if ($user_state !== null) {
            error_log("Warning: User $user_id had unexpected state '$user_state', clearing it");
            clearUserState($user_id);
        }
    }
}
?>